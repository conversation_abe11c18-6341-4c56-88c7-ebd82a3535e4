<?php
/**
 * Shortcodes for Custom Review Plugin
 */

// Prevent direct access
if (!defined('ABSPATH')) {
    exit;
}

class CustomReview_Shortcodes {
    
    private $database;
    
    public function __construct() {
        $this->database = new CustomReview_Database();
        $this->init_shortcodes();
    }
    
    /**
     * Initialize shortcodes
     */
    private function init_shortcodes() {
        add_shortcode('custom_reviews', array($this, 'display_reviews'));
        add_shortcode('custom_review_form', array($this, 'display_review_form'));
        add_shortcode('custom_review_stats', array($this, 'display_review_stats'));
        add_shortcode('custom_review_average', array($this, 'display_average_rating'));
        add_shortcode('custom_review_count', array($this, 'display_review_count'));
    }
    
    /**
     * Display reviews list
     */
    public function display_reviews($atts) {
        $atts = shortcode_atts(array(
            'limit' => 10,
            'show_form' => 'true',
            'show_stats' => 'true'
        ), $atts);
        
        $reviews = $this->database->get_reviews(array(
            'limit' => intval($atts['limit'])
        ));
        
        ob_start();
        ?>
        <div class="custom-reviews-container" dir="rtl">
            <?php if ($atts['show_stats'] === 'true'): ?>
                <div class="custom-reviews-stats">
                    <?php echo $this->display_review_stats(array()); ?>
                </div>
            <?php endif; ?>
            
            <?php if ($atts['show_form'] === 'true'): ?>
                <div class="custom-review-form-container">
                    <?php echo $this->display_review_form(); ?>
                </div>
            <?php endif; ?>
            
            <div class="custom-reviews-list">
                <h3><?php _e('المراجعات', 'custom-review'); ?></h3>
                
                <?php if (empty($reviews)): ?>
                    <p class="no-reviews"><?php _e('لا توجد مراجعات حتى الآن. كن أول من يضيف مراجعة!', 'custom-review'); ?></p>
                <?php else: ?>
                    <?php foreach ($reviews as $review): ?>
                        <div class="custom-review-item" data-review-id="<?php echo esc_attr($review->id); ?>">
                            <div class="review-header">
                                <div class="reviewer-info">
                                    <span class="reviewer-name"><?php echo esc_html($review->display_name); ?></span>
                                    <span class="review-date"><?php echo esc_html(date_i18n(get_option('date_format'), strtotime($review->created_at))); ?></span>
                                </div>
                                <div class="review-rating">
                                    <?php echo $this->render_stars($review->rating); ?>
                                </div>
                            </div>
                            <div class="review-content">
                                <p><?php echo wp_kses_post(nl2br($review->comment)); ?></p>
                            </div>
                        </div>
                    <?php endforeach; ?>
                <?php endif; ?>
            </div>
        </div>
        <?php
        return ob_get_clean();
    }
    
    /**
     * Display review form
     */
    public function display_review_form($atts) {
        $atts = shortcode_atts(array(
            'title' => __('أضف مراجعتك', 'custom-review')
        ), $atts);
        
        $current_user = wp_get_current_user();
        $is_logged_in = is_user_logged_in();
        
        // Check if user already reviewed
        $has_reviewed = false;
        if ($is_logged_in) {
            $has_reviewed = $this->database->user_has_reviewed($current_user->ID);
        }
        
        ob_start();
        ?>
        <div class="custom-review-form-wrapper" dir="rtl">
            <h3><?php echo esc_html($atts['title']); ?></h3>
            
            <?php if ($has_reviewed): ?>
                <p class="already-reviewed"><?php _e('لقد قمت بإضافة مراجعة من قبل. شكراً لك!', 'custom-review'); ?></p>
            <?php else: ?>
                <form id="custom-review-form" class="custom-review-form">
                    <?php wp_nonce_field('custom_review_submit', 'custom_review_nonce'); ?>
                    
                    <div class="form-group rating-group">
                        <label><?php _e('التقييم:', 'custom-review'); ?> <span class="required">*</span></label>
                        <div class="star-rating">
                            <?php for ($i = 5; $i >= 1; $i--): ?>
                                <input type="radio" name="rating" value="<?php echo $i; ?>" id="star<?php echo $i; ?>" />
                                <label for="star<?php echo $i; ?>" class="star">★</label>
                            <?php endfor; ?>
                        </div>
                    </div>
                    
                    <div class="form-group">
                        <label for="review-comment"><?php _e('التعليق:', 'custom-review'); ?> <span class="required">*</span></label>
                        <textarea 
                            id="review-comment" 
                            name="comment" 
                            rows="4" 
                            placeholder="<?php esc_attr_e('شاركنا رأيك وتجربتك...', 'custom-review'); ?>"
                            required
                        ></textarea>
                    </div>
                    
                    <?php if (!$is_logged_in): ?>
                        <div class="login-notice">
                            <p><?php _e('يجب تسجيل الدخول لإضافة مراجعة', 'custom-review'); ?></p>
                        </div>
                    <?php endif; ?>
                    
                    <div class="form-group submit-group">
                        <button type="submit" class="submit-review-btn">
                            <?php _e('إرسال المراجعة', 'custom-review'); ?>
                        </button>
                    </div>
                    
                    <div class="form-messages"></div>
                </form>
            <?php endif; ?>
        </div>
        <?php
        return ob_get_clean();
    }
    
    /**
     * Display review statistics
     */
    public function display_review_stats($atts) {
        $average = $this->database->get_average_rating();
        $count = $this->database->get_review_count();
        $distribution = $this->database->get_rating_distribution();
        
        ob_start();
        ?>
        <div class="custom-review-stats" dir="rtl">
            <div class="stats-summary">
                <div class="average-rating">
                    <span class="rating-number"><?php echo esc_html($average); ?></span>
                    <div class="rating-stars"><?php echo $this->render_stars($average); ?></div>
                    <span class="rating-count"><?php printf(_n('مراجعة واحدة', '%s مراجعة', $count, 'custom-review'), number_format_i18n($count)); ?></span>
                </div>
            </div>
            
            <div class="rating-distribution">
                <?php foreach ($distribution as $rating => $count): ?>
                    <div class="rating-bar">
                        <span class="rating-label"><?php echo $rating; ?> ★</span>
                        <div class="bar-container">
                            <div class="bar-fill" style="width: <?php echo $count > 0 ? ($count / array_sum($distribution)) * 100 : 0; ?>%"></div>
                        </div>
                        <span class="rating-count"><?php echo $count; ?></span>
                    </div>
                <?php endforeach; ?>
            </div>
        </div>
        <?php
        return ob_get_clean();
    }
    
    /**
     * Display average rating only
     */
    public function display_average_rating($atts) {
        $average = $this->database->get_average_rating();
        return '<span class="custom-review-average">' . $this->render_stars($average) . ' ' . $average . '</span>';
    }
    
    /**
     * Display review count only
     */
    public function display_review_count($atts) {
        $count = $this->database->get_review_count();
        return '<span class="custom-review-count">' . sprintf(_n('مراجعة واحدة', '%s مراجعة', $count, 'custom-review'), number_format_i18n($count)) . '</span>';
    }
    
    /**
     * Render star rating
     */
    private function render_stars($rating, $max_stars = 5) {
        $output = '<div class="stars-display">';
        
        for ($i = 1; $i <= $max_stars; $i++) {
            if ($i <= $rating) {
                $output .= '<span class="star filled">★</span>';
            } elseif ($i - 0.5 <= $rating) {
                $output .= '<span class="star half">★</span>';
            } else {
                $output .= '<span class="star empty">☆</span>';
            }
        }
        
        $output .= '</div>';
        return $output;
    }
}
